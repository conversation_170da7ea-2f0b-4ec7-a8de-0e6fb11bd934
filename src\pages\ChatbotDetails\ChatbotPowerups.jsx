import React, { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

import Bubble from '@/components/Bubble';
import PowerUps from '@/components/Chatbot/PowerUps';
import DButton from '@/components/Global/DButton';
import { COMMON_CLASSNAMES } from '@/constants';
import useCustomizationData from '@/hooks/useCustomization';
import LayoutRightSidebar from '@/layouts/LayoutRightSidebar';
import LayoutWithButtons from '@/layouts/LayoutWithButtons';
import { useCustomizationStore } from '@/stores/customization/customizationStore';
import useLayoutStore from '@/stores/layout/layoutStore';
import BlurredOverlay from '@/components/BlurredOverlay';
import { checkTeamManagementPermission } from '@/helpers/tier/featureCheck';
import ReactRouterPrompt from 'react-router-prompt';
import DConfirmationModal from '@/components/Global/DConfirmationModal';
import compareObjects from '@/helpers/compareObjects';
import DLoading from '@/components/DLoading';
import { updateChatbotPowerUps } from '@/services/customization.service';
import useToast  from '@/hooks/useToast';
import { useUserStore } from '@/stores/user/userStore';
import { trackKlaviyoEvent } from '@/services/chatbot.service';
import StyleTag from '@/components/StyleTag';

const ChatbotPowerups = () => {
  let params = useParams();
  const navigate = useNavigate();
  const setSidebarOpen = useLayoutStore((state) => state.setSidebarOpen);
  const setProgressBar = useLayoutStore((state) => state.setProgressBar);
  const setIsInPreviewBubblePage = useLayoutStore(
    (state) => state.setIsInPreviewBubblePage
  );
  const { addSuccessToast, addErrorToast } = useToast();

  const { customizationData, setChatbotCustomization, savingChanges } =
    useCustomizationData(true, params.id);

  const [tempCustomization, setTempCustomization] = useState(customizationData);
  const [isSaveLoading, setIsSaveLoading] = useState(false);
  const [unsavedChanges, setUnsavedChanges] = useState(false);
  const [errorLeadGenFields, setErrorLeadGenFields] = useState(false);
  const [errorHumanHandoverSettings, setErrorHumanHandoverSettings] = useState({
    availability: false,
    visibility: false
  });

  const [changedData, setChangedData] = useState({});

  const handleSave = async () => {
    setIsSaveLoading(true);

    // Reset all errors first
    setErrorLeadGenFields(false);
    setErrorHumanHandoverSettings({ availability: false, visibility: false });

    // Validate lead generation fields
    if(changedData.data_collection && (changedData.data_collection_fields?.length === 0 || !changedData.data_collection_fields)){
      setErrorLeadGenFields(true);
      setIsSaveLoading(false);
      return;
    }

    // Validate human handover settings when human handover is enabled
    if(tempCustomization.talk_to_live_agent) {
      const validationErrors = { availability: false, visibility: false };
      let hasValidationError = false;

      // Check if availability is selected
      if(!tempCustomization.live_agent_availability || tempCustomization.live_agent_availability === '') {
        validationErrors.availability = true;
        hasValidationError = true;
      }

      // Check if human handover visibility is selected
      const hasVisibilitySelection = tempCustomization.show_live_agent_on_conversation_begin ||
                                   tempCustomization.show_live_agent_rule;
      if(!hasVisibilitySelection) {
        validationErrors.visibility = true;
        hasValidationError = true;
      }

      if(hasValidationError) {
        setErrorHumanHandoverSettings(validationErrors);
        addErrorToast({
          message: 'Please select all required human handover settings before saving.',
        });
        setIsSaveLoading(false);
        return;
      }
    }
    try{
      const res = await updateChatbotPowerUps(params.id, changedData);
      if(res.status === 200){
        // Track customized-powerup event
        const user = useUserStore.getState().user;
        await trackKlaviyoEvent('customized-powerup', { 
          chatbot_id: params.id
        });
        setChatbotCustomization(res.data);
        setTempCustomization(res.data);
        addSuccessToast({
          message: 'Power-ups updated successfully',
        });
        setIsSaveLoading(false);
      }
    }catch(e){
      setIsSaveLoading(false);
      console.log('error', e)
    }finally{
      setIsSaveLoading(false);
      setErrorLeadGenFields(false);
    }
  };

  useEffect(() => {
    setSidebarOpen(false);
    setProgressBar([]);
    setIsInPreviewBubblePage(true);
  }, []);

  useEffect(() => {
    setTempCustomization(customizationData);
  }, [customizationData]);

  useEffect(() => {
    if (!tempCustomization || !customizationData) {
      return;
    }

    const hasUnsavedChanges = !compareObjects(
      customizationData,
      tempCustomization
    );

    setUnsavedChanges(hasUnsavedChanges);
  }, [tempCustomization]);

  if (!customizationData || !tempCustomization?.kb_id) {
    return <DLoading show={true} />;
  }

  return (
    <>
      <LayoutRightSidebar
        RightSidebar={() => (
          <div className={COMMON_CLASSNAMES.previewBubble}>
            <StyleTag tag=".bubble" tempCustomizationData={tempCustomization} />
            <Bubble
              type="chatbot"
              config={{
                ...tempCustomization,
                public: true,
                home_tab_enabled: false,
                remove_watermark: true,
              }}
              isPreviewMode={true}
              isInApp={false}
            />
          </div>
        )}
      >
        {() => (
            <LayoutWithButtons
              footer={
                <div className="flex items-center justify-between">
                  <DButton
                    variant="grey"
                    className="!h-12 w-full md:w-auto md:min-w-32 xl:!min-w-52"
                    onClick={() => navigate(`/chatbot/${params.id}`)}
                  >
                    Cancel
                  </DButton>
                  <DButton
                    variant="dark"
                    className="!h-12 w-full md:w-auto md:min-w-32 xl:!min-w-52"
                    onClick={handleSave}
                    loading={isSaveLoading}
                    disabled={compareObjects(
                      customizationData,
                      tempCustomization
                    )}
                  >
                    Save
                  </DButton>
                </div>
              }
            >
              <PowerUps
                customizationData={tempCustomization}
                updateCustomizationData={(key, value) =>{
                  setTempCustomization((prev) => ({ ...prev, [key]: value }))
                  setChangedData((prev) => ({ ...prev, [key]: value }))
                }}
                errorLeadGenFields={errorLeadGenFields}
                setErrorLeadGenFields={setErrorLeadGenFields}
                errorHumanHandoverSettings={errorHumanHandoverSettings}
                setErrorHumanHandoverSettings={setErrorHumanHandoverSettings}
              />
              <ReactRouterPrompt when={unsavedChanges}>
                {({ isActive, onConfirm, onCancel }) => (
                  <DConfirmationModal
                    open={isActive}
                    onClose={onCancel}
                    onConfirm={onConfirm}
                    title="Are you sure you want to leave this page?"
                    description="You have unsaved changes. If you leave, you will lose your changes."
                    confirmText="Leave"
                    cancelText="Cancel"
                    variantConfirm="danger"
                  />
                )}
              </ReactRouterPrompt>
            </LayoutWithButtons>
        )}
      </LayoutRightSidebar>
    </>
  );
};

export default ChatbotPowerups;
